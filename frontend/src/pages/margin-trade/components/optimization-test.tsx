import { useEffect, useState } from 'react'

/**
 * 🧪 K线图数据加载优化测试组件
 * 用于验证API调用次数和SSE连接数量
 */
export function OptimizationTest() {
  const [apiCallCount, setApiCallCount] = useState(0)
  const [sseConnectionCount, setSseConnectionCount] = useState(0)
  const [testResults, setTestResults] = useState<string[]>([])

  useEffect(() => {
    // 监听网络请求
    const originalFetch = window.fetch
    let klineApiCalls = 0
    
    window.fetch = async (...args) => {
      const url = args[0]?.toString() || ''
      
      // 检测 K线 API 调用
      if (url.includes('/kline/getMarginTradeKline')) {
        klineApiCalls++
        setApiCallCount(klineApiCalls)
        setTestResults(prev => [...prev, `🔍 API调用 #${klineApiCalls}: ${url}`])
      }
      
      return originalFetch(...args)
    }

    // 监听 SSE 连接
    const originalEventSource = window.EventSource
    let sseConnections = 0
    
    window.EventSource = class extends originalEventSource {
      constructor(url: string | URL, eventSourceInitDict?: EventSourceInit) {
        super(url, eventSourceInitDict)
        sseConnections++
        setSseConnectionCount(sseConnections)
        setTestResults(prev => [...prev, `🔗 SSE连接 #${sseConnections}: ${url}`])
      }
    }

    return () => {
      window.fetch = originalFetch
      window.EventSource = originalEventSource
    }
  }, [])

  const clearResults = () => {
    setApiCallCount(0)
    setSseConnectionCount(0)
    setTestResults([])
  }

  return (
    <div className="fixed top-4 right-4 bg-black/80 text-white p-4 rounded-lg max-w-md z-50">
      <h3 className="text-lg font-bold mb-2">🧪 优化测试面板</h3>
      
      <div className="space-y-2 mb-4">
        <div className="flex justify-between">
          <span>API调用次数:</span>
          <span className={apiCallCount <= 1 ? 'text-green-400' : 'text-red-400'}>
            {apiCallCount}
          </span>
        </div>
        <div className="flex justify-between">
          <span>SSE连接数:</span>
          <span className={sseConnectionCount <= 1 ? 'text-green-400' : 'text-red-400'}>
            {sseConnectionCount}
          </span>
        </div>
      </div>

      <div className="mb-4">
        <div className="text-sm font-semibold mb-1">测试结果:</div>
        <div className="text-xs space-y-1 max-h-32 overflow-y-auto">
          {testResults.length === 0 ? (
            <div className="text-gray-400">等待网络活动...</div>
          ) : (
            testResults.map((result, index) => (
              <div key={index} className="break-all">
                {result}
              </div>
            ))
          )}
        </div>
      </div>

      <div className="space-y-2">
        <button
          onClick={clearResults}
          className="w-full bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-sm"
        >
          清除结果
        </button>
        
        <div className="text-xs text-gray-300">
          <div>✅ 期望: API调用 ≤ 1次</div>
          <div>✅ 期望: SSE连接 ≤ 1个</div>
        </div>
      </div>
    </div>
  )
}

/**
 * 🔧 开发环境下的调试工具
 * 在控制台中提供额外的调试信息
 */
export function setupDebugTools() {
  if (process.env.NODE_ENV === 'development') {
    // 全局调试函数
    (window as any).debugKlineOptimization = {
      // 检查当前活跃的网络连接
      checkActiveConnections: () => {
        console.log('🔍 检查活跃连接...')
        // 这里可以添加更多调试逻辑
      },
      
      // 模拟SSE数据
      simulateSSEData: (data: any) => {
        console.log('📡 模拟SSE数据:', data)
        // 可以用于测试实时更新逻辑
      }
    }
    
    console.log('🔧 K线优化调试工具已加载')
    console.log('使用 window.debugKlineOptimization 访问调试功能')
  }
}
