import { CandlestickChart } from '@/components/chart/candlestick-chart'
import { ResolutionKey } from '@/components/chart/types'
import { useState, useCallback, useRef, useEffect } from 'react'
import type { LogicalRange } from 'lightweight-charts'
import type { IChartApi } from 'lightweight-charts'
import type { CandlestickChartData } from '../hooks/use-margin-trade-kline'
// import { SUI_COIN_TYPE } from '@/lib/coin'
import { useMarginTradeKline } from '../hooks/use-margin-trade-kline'

interface ChartProps {
  realtimeEnabled?: boolean
  coinType: string
  useSSE?: boolean
}

export function Chart({
  realtimeEnabled = true,
  coinType,
  useSSE = true
}: ChartProps) {
  console.log('🔄 Chart component rendered with:', {
    coinType,
    realtimeEnabled,
    useSSE
  })

  const [bar, setBar] = useState<ResolutionKey>(ResolutionKey.Min15)

  // 图表引用
  const chartRef = useRef<IChartApi | null>(null)
  const seriesRef = useRef<{
    update: (data: CandlestickChartData) => void
    setData: (data: CandlestickChartData[]) => void
  } | null>(null)

  // 🔥 实时数据更新回调
  const handleRealtimeUpdate = useCallback(
    (newCandleData: CandlestickChartData) => {
      if (seriesRef.current) {
        console.log('📊 Incremental update to chart:', newCandleData)
        debugger
        // 🔥 使用candleSeries.update进行增量更新
        seriesRef.current.update(newCandleData)
      }
    },
    []
  )

  // 🔥 使用新的统一数据管理 hook
  const {
    chartData,
    // isInitialLoading,
    isHistoryLoading,
    initialDataLoaded,
    sseConnectionStatus,
    isRealtime,
    loadMoreHistory,
    hasMoreData,
    error
  } = useMarginTradeKline(coinType, bar, {
    enabled: !!coinType,
    realtimeEnabled,
    useSSE,
    autoReconnect: true,
    onRealtimeUpdate: handleRealtimeUpdate // 🔥 传递回调函数
  })

  // 🔥 处理可见范围变化，触发历史数据加载
  const handleVisibleRangeChange = useCallback(
    (logicalRange: LogicalRange) => {
      if (
        Number(logicalRange.from) < 10 &&
        !isHistoryLoading &&
        hasMoreData &&
        initialDataLoaded
      ) {
        // console.log('📈 Triggering historical data load from visible range change')
        loadMoreHistory()
      }
    },
    [loadMoreHistory, isHistoryLoading, hasMoreData, initialDataLoaded]
  )

  // 🔥 图表回调函数
  const handleChartReady = useCallback((chart: IChartApi) => {
    console.log('📊 Chart ready callback triggered')
    chartRef.current = chart
  }, [])

  const handleSeriesReady = useCallback(
    (series: {
      update: (data: CandlestickChartData) => void
      setData: (data: CandlestickChartData[]) => void
  }) => {
    console.log('📊 Series ready callback triggered')
    seriesRef.current = series
    },
    []
  )

  // 🔥 监听chartData变化，只处理初始数据加载
  useEffect(() => {
    if (seriesRef.current && chartData && chartData.length > 0) {
      console.log('📊 Setting initial chart data:', chartData.length, 'items')
      // 🔥 只处理初始数据加载，使用setData设置完整数据
      seriesRef.current.setData(chartData)
    }
  }, [chartData])

  return (
    <div className="relative">
      {/* SSE连接状态指示器 */}
      {realtimeEnabled && useSSE && (
        <div className="absolute top-2 right-2 z-10 flex flex-col gap-1">
          <div className="flex items-center gap-2 px-2 py-1 bg-black/50 rounded text-xs text-white">
            <div
              className={`w-2 h-2 rounded-full ${
                sseConnectionStatus === 'connected'
                      ? 'bg-green-500'
                  : sseConnectionStatus === 'connecting'
                        ? 'bg-yellow-500 animate-pulse'
                    : sseConnectionStatus === 'error'
                          ? 'bg-red-500'
                          : 'bg-gray-500'
              }`}
            />
            <span>
              {sseConnectionStatus === 'connected'
                    ? 'Real-time Data'
                : sseConnectionStatus === 'connecting'
                      ? 'Connecting...'
                  : sseConnectionStatus === 'error'
                        ? 'Connection Error'
                        : 'Disconnected'}
            </span>
            {isRealtime && <span className="text-green-400">●</span>}
          </div>

          {/* 错误信息显示 */}
          {error && sseConnectionStatus === 'error' && (
            <div className="px-2 py-1 bg-red-500/80 rounded text-xs text-white max-w-xs">
              <div className="text-gray-200 truncate">{error}</div>
            </div>
          )}
        </div>
      )}

        <CandlestickChart
          data={chartData}
          currentResolution={bar}
          onResolutionChange={setBar}
          onVisibleRangeChange={handleVisibleRangeChange}
          onChartReady={handleChartReady}
          onSeriesReady={handleSeriesReady}
        isLoadingMore={isHistoryLoading}
          className="border border-border-8 rounded-[10px] w-[936px] h-[400px] flex flex-col items-center justify-center p-4"
        />
    </div>
  )
}
