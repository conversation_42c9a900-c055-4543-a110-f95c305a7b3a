import { useState, useEffect, useRef, useCallback, useMemo } from 'react'
import { useGetMarginTradeKline } from '@/queries/kline'
import { useKlineSSE, type SSEError } from '@/hooks/use-kline-sse'
import { ResolutionKey, BAR_DURATION_MAP } from '@/components/chart/types'
import type { IKLineInfo } from '@/types/kline'
import type { UTCTimestamp } from 'lightweight-charts'
import KLineService from '@/services/kline'

export interface CandlestickChartData {
  time: string | UTCTimestamp
  open: number
  high: number
  low: number
  close: number
}

export interface UseMarginTradeKlineOptions {
  enabled?: boolean
  realtimeEnabled?: boolean
  useSSE?: boolean
  autoReconnect?: boolean
  onRealtimeUpdate?: (data: CandlestickChartData) => void // 🔥 新增：实时数据更新回调
}

export interface UseMarginTradeKlineReturn {
  // 图表数据
  chartData: CandlestickChartData[]
  
  // 价格数据
  currentPrice: number
  priceChange24h: number
  priceChangePercent24h: number
  
  // 加载状态
  isInitialLoading: boolean
  isHistoryLoading: boolean
  initialDataLoaded: boolean
  
  // SSE 状态
  sseConnectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error'
  isRealtime: boolean
  
  // 方法
  loadMoreHistory: () => Promise<void>
  updateRealtimeData: (klineData: IKLineInfo) => void
  
  // 错误状态
  error: string | null
  
  // 图表相关
  hasMoreData: boolean
}

export const useMarginTradeKline = (
  coinType: string,
  bar: ResolutionKey = ResolutionKey.Min15,
  options: UseMarginTradeKlineOptions = {}
): UseMarginTradeKlineReturn => {
  const {
    enabled = true,
    realtimeEnabled = true,
    useSSE = true,
    autoReconnect = true,
    onRealtimeUpdate
  } = options

  // 状态管理
  const [chartData, setChartData] = useState<CandlestickChartData[]>([])
  const [initialDataLoaded, setInitialDataLoaded] = useState(false)
  const [isHistoryLoading, setIsHistoryLoading] = useState(false)
  const [realtimeData, setRealtimeData] = useState<IKLineInfo | null>(null)
  
  // 数据管理 refs
  const lastFromTime = useRef<number>(0)
  const hasMoreData = useRef<boolean>(true)
  const lastUpdateTime = useRef<number>(0)
  
  // 常量配置
  const initialSize = 300
  const fetchSize = initialSize + 1
  const additionalSize = 50

  // 计算初始数据的时间范围
  const { toTime, fromTime } = useMemo(() => {
    const toTime = Date.now()
    const fromTime = toTime - initialSize * BAR_DURATION_MAP[bar]
    return { toTime, fromTime }
  }, [bar])

  // 🔥 步骤1：初始数据加载
  const {
    data: initialData,
    isLoading: isInitialLoading,
    error: initialError,
    isSuccess: isInitialSuccess
  } = useGetMarginTradeKline(
    {
      address: coinType,
      bar,
      fromTime,
      toTime,
      size: fetchSize
    },
    {
      enabled: enabled && !!coinType && !initialDataLoaded
    }
  )

  // 🔥 步骤2：处理初始数据加载完成
  useEffect(() => {
    if (isInitialSuccess && initialData?.data && initialData.data.length > 0) {
      console.log('📊 Initial kline data loaded:', initialData.data.length, 'items')
      
      // 转换数据格式
      const formattedData: CandlestickChartData[] = initialData.data.map((item) => ({
        time: (item.t / 1000) as UTCTimestamp,
        open: item.o,
        high: item.h,
        low: item.l,
        close: item.c
      }))
      
      setChartData(formattedData)
      setInitialDataLoaded(true)
      
      // 设置历史数据加载的起始时间
      if (initialData.data.length > 0) {
        lastFromTime.current = initialData.data[0].t
        lastUpdateTime.current = initialData.data[initialData.data.length - 1].t
      }
      
      // 检查是否还有更多历史数据
      hasMoreData.current = initialData.data.length >= fetchSize
      
    } else if (!isInitialLoading && !initialData?.data) {
      setInitialDataLoaded(false)
      setChartData([])
    }
  }, [isInitialSuccess, initialData, isInitialLoading])

  // 🔥 步骤3：SSE 实时数据更新回调
  const handleKlineUpdate = useCallback((klineData: IKLineInfo) => {
    // 防止重复更新：检查时间戳和币种
    if (
      klineData.t > lastUpdateTime.current &&
      klineData.address === coinType
    ) {
      console.log('📈 Received realtime kline update:', klineData)
      lastUpdateTime.current = klineData.t
      setRealtimeData(klineData)
      
      // 🔥 转换数据格式
      const newCandleData: CandlestickChartData = {
        time: (klineData.t / 1000) as UTCTimestamp,
        open: klineData.o,
        high: klineData.h,
        low: klineData.l,
        close: klineData.c
      }
      
      // 🔥 优先使用回调函数进行增量更新
      if (onRealtimeUpdate) {
        console.log('📊 Using callback for incremental update:', newCandleData)
        onRealtimeUpdate(newCandleData)
      } else {
        // 🔥 备用方案：更新状态（用于向后兼容）
        setChartData(prevData => {
          const newData = [...prevData]
          const lastIndex = newData.length - 1
          
          // 如果是同一时间段的数据，更新最后一条
          if (lastIndex >= 0 && newData[lastIndex].time === newCandleData.time) {
            newData[lastIndex] = newCandleData
          } else {
            // 否则添加新数据
            newData.push(newCandleData)
          }
          
          return newData
        })
      }
    }
  }, [coinType, onRealtimeUpdate])

  // 🔥 SSE 错误处理
  const handleSSEError = useCallback((error: SSEError) => {
    console.error('❌ SSE connection error:', error)
  }, [])

  // 🔥 步骤4：SSE 连接 - 只有在初始数据加载完成后才连接
  const {
    connectionStatus: sseConnectionStatus,
    disconnect: sseDisconnect,
    lastError: sseError
  } = useKlineSSE({
    symbol: coinType,
    bar,
    enabled: useSSE && realtimeEnabled && initialDataLoaded && !!coinType,
    onKlineUpdate: handleKlineUpdate,
    onError: handleSSEError,
    throttleMs: 1000,
    autoReconnect
  })

  // 🔥 步骤5：历史数据加载函数 - 只调用API，不启动SSE
  const loadMoreHistory = useCallback(async () => {
    if (
      isHistoryLoading ||
      !hasMoreData.current ||
      !coinType ||
      !initialDataLoaded
    ) {
      return
    }

    console.log('📈 Loading more historical data - coinType:', coinType, 'bar:', bar)
    setIsHistoryLoading(true)

    if (lastFromTime.current === 0) {
      lastFromTime.current = fromTime
    }

    const historicalFromTime = lastFromTime.current - additionalSize * BAR_DURATION_MAP[bar]

    try {
      const result = await KLineService.getMarginTradeKline.call({
        address: coinType,
        bar,
        fromTime: historicalFromTime,
        toTime: lastFromTime.current,
        size: additionalSize
      })

      if (result?.data && result.data.length > 0) {
        console.log('📊 Historical data loaded:', result.data.length, 'items')

        // 转换数据格式
        const formattedHistoricalData: CandlestickChartData[] = result.data.map((item) => ({
          time: (item.t / 1000) as UTCTimestamp,
          open: item.o,
          high: item.h,
          low: item.l,
          close: item.c
        }))

        // 将历史数据添加到现有数据前面
        setChartData(prevData => [...formattedHistoricalData, ...prevData])

        // 更新历史数据加载的起始时间
        lastFromTime.current = result.data[0].t

        // 检查是否还有更多历史数据
        hasMoreData.current = result.data.length >= additionalSize
      } else {
        console.log('📊 No more historical data available')
        hasMoreData.current = false
      }
    } catch (error) {
      console.error('❌ Failed to load historical data:', error)
      hasMoreData.current = false
    } finally {
      setIsHistoryLoading(false)
    }
  }, [isHistoryLoading, coinType, initialDataLoaded, bar, fromTime])

  // 🔥 手动更新实时数据的方法（供外部调用）
  const updateRealtimeData = useCallback((klineData: IKLineInfo) => {
    handleKlineUpdate(klineData)
  }, [handleKlineUpdate])

  // 🔥 计算价格数据
  const priceData = useMemo(() => {
    if (!chartData || chartData.length === 0) {
      return {
        currentPrice: 0,
        priceChange24h: 0,
        priceChangePercent24h: 0
      }
    }

    // 优先使用实时数据作为当前价格
    let currentPrice: number
    if (realtimeData && realtimeData.address === coinType) {
      currentPrice = realtimeData.c
    } else {
      currentPrice = chartData[chartData.length - 1].close
    }

    // 获取24小时前的价格（第一个K线数据的开盘价）
    const price24hAgo = chartData[0].open

    // 计算价格变化
    const priceChange24h = currentPrice - price24hAgo
    const priceChangePercent24h = price24hAgo > 0 ? (priceChange24h / price24hAgo) * 100 : 0

    return {
      currentPrice,
      priceChange24h,
      priceChangePercent24h
    }
  }, [chartData, realtimeData, coinType])

  // 🔥 coinType变化时重置所有状态
  useEffect(() => {
    if (coinType) {
      console.log('🔄 CoinType changed, resetting states:', coinType)
      setChartData([])
      setInitialDataLoaded(false)
      setRealtimeData(null)
      setIsHistoryLoading(false)
      lastFromTime.current = 0
      hasMoreData.current = true
      lastUpdateTime.current = 0
    }
  }, [coinType])

  // 🔥 组件卸载时清理 SSE 连接
  useEffect(() => {
    return () => {
      console.log('🧹 Cleaning up margin trade kline hook')
      if (sseDisconnect) {
        sseDisconnect()
      }
    }
  }, [sseDisconnect])

  return {
    // 图表数据
    chartData,

    // 价格数据
    currentPrice: priceData.currentPrice,
    priceChange24h: priceData.priceChange24h,
    priceChangePercent24h: priceData.priceChangePercent24h,

    // 加载状态
    isInitialLoading,
    isHistoryLoading,
    initialDataLoaded,

    // SSE 状态
    sseConnectionStatus,
    isRealtime: sseConnectionStatus === 'connected' && !!realtimeData,

    // 方法
    loadMoreHistory,
    updateRealtimeData,

    // 错误状态
    error: initialError?.message || sseError?.message || null,

    // 图表相关
    hasMoreData: hasMoreData.current
  }
}
