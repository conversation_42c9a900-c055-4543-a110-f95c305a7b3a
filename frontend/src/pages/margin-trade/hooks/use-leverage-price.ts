// import { useMemo } from 'react'
import { ResolutionKey } from '@/components/chart/types'
import { useMarginTradeKline } from './use-margin-trade-kline'

export interface ILeveragePriceResult {
  currentPrice: number
  priceChange24h: number
  priceChangePercent24h: number
  isLoading: boolean
  error: string | null
  // SSE 相关状态
  sseConnectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error'
  isRealtime: boolean
}

export const useLeveragePrice = (
  coinType: string,
  options?: {
    refetchInterval?: number | false
    enabled?: boolean
    useSSE?: boolean
    autoReconnect?: boolean
  }
): ILeveragePriceResult => {
  console.log('🔄 useLeveragePrice called with:', {
    coinType,
    enabled: options?.enabled,
    useSSE: options?.useSSE
  })

  // 🔥 使用新的统一数据管理 hook
  const {
    currentPrice,
    priceChange24h,
    priceChangePercent24h,
    isInitialLoading,
    sseConnectionStatus,
    isRealtime,
    error
  } = useMarginTradeKline(coinType, ResolutionKey.Min15, {
    enabled: options?.enabled !== false && !!coinType,
    realtimeEnabled: options?.useSSE !== false,
    useSSE: options?.useSSE !== false,
    autoReconnect: options?.autoReconnect !== false
  })

  return {
    currentPrice,
    priceChange24h,
    priceChangePercent24h,
    isLoading: isInitialLoading,
    error,
    sseConnectionStatus,
    isRealtime
  }
}
