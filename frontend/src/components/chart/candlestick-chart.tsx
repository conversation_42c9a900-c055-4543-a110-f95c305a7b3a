import { useEffect, useRef, useState, type FC } from 'react'
import {
  createChart,
  CandlestickSeries,
  type CandlestickData,
  type Time,
  type UTCTimestamp,
  type LogicalRange,
  type IChartApi
} from 'lightweight-charts'
import { cn } from '@/lib/utils'
import type { ResolutionKey } from './types'
import { ResolutionsSelect } from './resolutions-select'

export interface CandlestickChartData {
  time: string | UTCTimestamp
  open: number
  high: number
  low: number
  close: number
}

interface CandlestickProps {
  data: CandlestickChartData[]
  className?: string
  currentResolution: ResolutionKey
  onResolutionChange?: (key: ResolutionKey) => void
  onVisibleRangeChange?: (logicalRange: LogicalRange) => void
  onChartReady?: (chart: IChartApi) => void
  onSeriesReady?: (series: {
    update: (data: CandlestickData<Time>) => void
    setData: (data: CandlestickData<Time>[]) => void
  }) => void
  isLoadingMore?: boolean
}

export const CandlestickChart: FC<CandlestickProps> = ({
  data,
  className,
  currentResolution,
  onResolutionChange,
  onVisibleRangeChange,
  onChartReady,
  onSeriesReady,
  isLoadingMore = false
}) => {
  const ref = useRef<HTMLDivElement>(null)
  const [currentData, setCurrentData] = useState<CandlestickChartData>()
  const [isLoading, setIsLoading] = useState(true)

  // 🔥 关键修复：使用useRef存储回调函数，避免依赖数组问题
  const onVisibleRangeChangeRef = useRef(onVisibleRangeChange)
  const onChartReadyRef = useRef(onChartReady)
  const onSeriesReadyRef = useRef(onSeriesReady)

  // 更新ref中的回调函数
  onVisibleRangeChangeRef.current = onVisibleRangeChange
  onChartReadyRef.current = onChartReady
  onSeriesReadyRef.current = onSeriesReady

  // 🔥 分离图表创建和数据更新逻辑
  useEffect(() => {
    if (!ref.current) return
    setIsLoading(true)

    const chart = createChart(ref.current, {
      layout: {
        background: { color: 'transparent' },
        textColor: '#DDD'
      },
      grid: {
        vertLines: { visible: false },
        horzLines: { visible: false }
      },
      timeScale: {
        tickMarkFormatter: (time: UTCTimestamp) => {
          const d = new Date(time * 1000)
          const m = String(d.getMonth() + 1).padStart(2, '0')
          const day = String(d.getDate()).padStart(2, '0')
          const h = String(d.getHours()).padStart(2, '0')
          const min = String(d.getMinutes()).padStart(2, '0')

          if (currentResolution.includes('m')) {
            return `${h}:${min}`
          } else if (currentResolution.includes('h')) {
            return `${m}-${day} ${h}`
          } else {
            return `${m}-${day}`
          }
        }
      },
      crosshair: {
        vertLine: {
          labelVisible: true
        }
      },
      localization: {
        timeFormatter: (time: UTCTimestamp) => {
          const d = new Date(time * 1000)
          const day = d.getDate()
          const month = d.toLocaleDateString('en-US', { month: 'short' })
          const year = String(d.getFullYear()).slice(-2)
          const h = String(d.getHours()).padStart(2, '0')
          const min = String(d.getMinutes()).padStart(2, '0')
          return `${day} ${month} '${year} ${h}:${min}`
        }
      }
    })
    window.chart = chart

    const mainSeries = chart.addSeries(CandlestickSeries)

    // 🔥 使用ref中的回调函数
    if (onChartReadyRef.current) {
      onChartReadyRef.current(chart)
    }

    if (onSeriesReadyRef.current) {
      onSeriesReadyRef.current(mainSeries)
    }

    chart.subscribeCrosshairMove((param) => {
      if (param.time) {
        const data = param.seriesData.get(mainSeries) as CandlestickData<Time>
        setCurrentData({
          time: data.time.toString(),
          open: data.open,
          close: data.close,
          high: data.high,
          low: data.low
        })
      }
    })

    chart.timeScale().subscribeVisibleLogicalRangeChange((logicalRange) => {
      if (logicalRange) {
        // 🔥 使用ref中的回调函数
        onVisibleRangeChangeRef.current?.(logicalRange)
      }
    })

    const handleResize = () => {
      chart.applyOptions({
        width: ref.current?.clientWidth,
        height: ref.current?.clientHeight
      })
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      chart.remove()
    }
  }, [currentResolution]) // 🔥 只在分辨率变化时重新创建图表

  // 🔥 单独处理数据更新
  useEffect(() => {
    if (data && data.length > 0) {
      setIsLoading(false)
      // 数据更新通过onSeriesReady回调中的series.update()方法处理
      // 这里不需要重新设置数据，因为数据更新已经在useMarginTradeKline中处理
    }
  }, [data])

  return (
    <div className={cn('flex flex-col relative', className)}>
      <div className="flex w-full border-b border-border-8 pb-2 gap-2 justify-between">
        <ResolutionsSelect
          value={currentResolution}
          onValueChange={onResolutionChange}
        />
        {currentData && (
          <div className="flex gap-1 items-center">
            <div
              className={cn(
                'text-sm',
                currentData?.close >= currentData?.open
                  ? 'text-green'
                  : 'text-red'
              )}>
              <span className="text-foreground">O</span>{' '}
              {currentData?.open.toFixed(2)}
              <span className="text-foreground"> H</span>{' '}
              {currentData?.high.toFixed(2)}
              <span className="text-foreground"> L</span>{' '}
              {currentData?.low.toFixed(2)}
              <span className="text-foreground"> C</span>{' '}
              {currentData?.close.toFixed(2)}
              <span>
                {' '}
                {(currentData?.close - currentData?.open).toFixed(4)}{' '}
                {`(${(((currentData?.close - currentData?.open) * 100) / currentData?.open).toFixed(2)}%)`}
              </span>
            </div>
          </div>
        )}
      </div>
      <div
        ref={ref}
        className={cn(
          'w-full h-full relative',
          (isLoading || isLoadingMore) &&
            'flex items-center justify-center bg-black/40'
        )}>
        {(isLoading || isLoadingMore) && (
          <div className="absolute text-base text-white opacity-90">
            {isLoadingMore ? 'Loading more data...' : 'Loading...'}
          </div>
        )}
      </div>
    </div>
  )
}
